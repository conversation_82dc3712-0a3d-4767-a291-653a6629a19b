package com.chervon.usercenter.application.impl;

import com.baomidou.mybatisplus.core.MybatisConfiguration;
import com.baomidou.mybatisplus.core.metadata.TableInfoHelper;
import com.chervon.common.core.prop.SfProperties;
import com.chervon.usercenter.api.dto.*;
import com.chervon.usercenter.api.exception.UserCenterException;
import com.chervon.usercenter.api.service.SaleForceService;
import com.chervon.usercenter.api.service.UserQueryService;
import com.chervon.usercenter.api.vo.sf.SfUserRecord;
import com.chervon.usercenter.domain.model.user.*;
import com.chervon.usercenter.infrastructure.entity.UserDo;
import com.chervon.usercenter.infrastructure.mq.SfUserMessage;
import com.chervon.usercenter.infrastructure.mq.StreamProducer;
import org.apache.ibatis.builder.MapperBuilderAssistant;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * UserCommandServiceImpl 单元测试
 *
 * <AUTHOR> Assistant
 * @date 2025-01-27
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("用户命令服务实现类单元测试")
class UserCommandServiceImplTest {

    @InjectMocks
    private UserCommandServiceImpl userCommandService;

    @Mock
    private UserRepository userRepository;

    @Mock
    private UserQueryService userQueryService;

    @Mock
    private StreamProducer streamProducer;

    @Mock
    private SfProperties sfProperties;

    @Mock
    private SaleForceService saleForceService;

    @Mock
    private User mockUser;

    @Mock
    private UserDo mockUserDo;

    @Mock
    private Password mockPassword;

    private UserRegisterDto userRegisterDto;
    private ConfirmPasswordDto confirmPasswordDto;
    private EditPasswordDto editPasswordDto;
    private ResetPasswordDto resetPasswordDto;
    private PhoneInfoDto phoneInfoDto;
    private UserEditDto userEditDto;

    @BeforeEach
    void setUp() {
        // 初始化 MyBatis 配置以避免测试错误
        MybatisConfiguration configuration = new MybatisConfiguration();
        TableInfoHelper.initTableInfo(new MapperBuilderAssistant(configuration, ""), UserDo.class);

        // 初始化测试数据
        userRegisterDto = new UserRegisterDto();
        userRegisterDto.setEmail("<EMAIL>");
        userRegisterDto.setPassword("password123");
        userRegisterDto.setCode("123456");
        userRegisterDto.setFirstName("John");
        userRegisterDto.setLastName("Doe");

        confirmPasswordDto = new ConfirmPasswordDto();
        confirmPasswordDto.setEmail("<EMAIL>");
        confirmPasswordDto.setPassword("encryptedPassword");

        editPasswordDto = new EditPasswordDto();
        editPasswordDto.setEmail("<EMAIL>");
        editPasswordDto.setOldPassword("oldEncryptedPassword");
        editPasswordDto.setPassword("newEncryptedPassword");

        resetPasswordDto = new ResetPasswordDto();
        resetPasswordDto.setEmail("<EMAIL>");
        resetPasswordDto.setPassword("newEncryptedPassword");
        resetPasswordDto.setCode("123456");

        phoneInfoDto = new PhoneInfoDto();
        phoneInfoDto.setPhoneModel("iPhone 12");

        userEditDto = new UserEditDto();
        userEditDto.setId(1L);
        userEditDto.setFirstName("Jane");
        userEditDto.setLastName("Smith");

        // Mock 对象设置
        when(mockUser.getUserId()).thenReturn(1L);
        when(mockUser.getPassword()).thenReturn(mockPassword);
        when(mockUserDo.getId()).thenReturn(1L);
    }

    @Test
    @DisplayName("用户注册 - 成功场景")
    void testRegister_Success() {
        // Given - 准备测试数据和Mock对象
        when(userQueryService.checkEmailUsed(userRegisterDto.getEmail())).thenReturn(false);
        when(sfProperties.isEnable()).thenReturn(false);

        // When - 执行被测试的方法
        boolean result = userCommandService.register(userRegisterDto);

        // Then - 验证执行结果
        assertTrue(result);
        verify(userQueryService).checkEmailUsed(userRegisterDto.getEmail());
    }

    @Test
    @DisplayName("用户注册 - 邮箱已被使用异常")
    void testRegister_EmailAlreadyUsed() {
        // Given - 准备邮箱已被使用的测试数据
        when(userQueryService.checkEmailUsed(userRegisterDto.getEmail())).thenReturn(true);

        // When - 执行被测试的方法
        boolean result = userCommandService.register(userRegisterDto);

        // Then - 验证执行结果
        assertFalse(result);
        verify(userQueryService).checkEmailUsed(userRegisterDto.getEmail());
    }

    @Test
    @DisplayName("确认密码 - 成功场景")
    void testConfirmPassword_Success() {
        // Given - 准备成功确认密码的测试数据
        Email testEmail = new Email(confirmPasswordDto.getEmail());
        when(userRepository.find(testEmail)).thenReturn(mockUser);

        // When - 执行确认密码方法
        boolean result = userCommandService.confirmPassword(confirmPasswordDto);

        // Then - 验证执行结果
        assertTrue(result);
        verify(userRepository).find(testEmail);
    }

    @Test
    @DisplayName("确认密码 - 用户不存在异常")
    void testConfirmPassword_UserNotFound() {
        // Given - 准备用户不存在的测试数据
        Email testEmail = new Email(confirmPasswordDto.getEmail());
        when(userRepository.find(testEmail)).thenReturn(null);

        // When & Then - 执行方法并验证异常
        assertThrows(UserCenterException.class, () -> userCommandService.confirmPassword(confirmPasswordDto));
        verify(userRepository).find(testEmail);
    }

    @Test
    @DisplayName("修改密码 - 成功场景")
    void testEditPassword_Success() {
        // Given - 准备成功修改密码的测试数据
        Email testEmail = new Email(editPasswordDto.getEmail());
        when(userRepository.find(testEmail)).thenReturn(mockUser);

        // When - 执行修改密码方法
        boolean result = userCommandService.editPassword(editPasswordDto);

        // Then - 验证执行结果
        assertTrue(result);
        verify(userRepository).find(testEmail);
    }

    @Test
    @DisplayName("修改密码 - 用户不存在异常")
    void testEditPassword_UserNotFound() {
        // Given - 准备用户不存在的测试数据
        Email testEmail = new Email(editPasswordDto.getEmail());
        when(userRepository.find(testEmail)).thenReturn(null);

        // When & Then - 执行方法并验证异常
        assertThrows(UserCenterException.class, () -> userCommandService.editPassword(editPasswordDto));
        verify(userRepository).find(testEmail);
    }

    @Test
    @DisplayName("重置密码 - 成功场景")
    void testResetPassword_Success() {
        // Given - 准备成功重置密码的测试数据
        Email testEmail = new Email(resetPasswordDto.getEmail());
        when(userRepository.find(testEmail)).thenReturn(mockUser);

        // When - 执行重置密码方法
        boolean result = userCommandService.resetPassword(resetPasswordDto);

        // Then - 验证执行结果
        assertTrue(result);
        verify(userRepository).find(testEmail);
    }

    @Test
    @DisplayName("重置密码 - 用户不存在异常")
    void testResetPassword_UserNotFound() {
        // Given - 准备用户不存在的测试数据
        Email testEmail = new Email(resetPasswordDto.getEmail());
        when(userRepository.find(testEmail)).thenReturn(null);

        // When & Then - 执行方法并验证异常
        assertThrows(UserCenterException.class, () -> userCommandService.resetPassword(resetPasswordDto));
        verify(userRepository).find(testEmail);
    }

    @Test
    @DisplayName("注销用户 - 成功场景")
    void testLogOffUser_Success() {
        // Given - 准备成功注销用户的测试数据
        Email testEmail = new Email("<EMAIL>");
        when(userRepository.find(testEmail)).thenReturn(mockUser);

        // When - 执行注销用户方法
        boolean result = userCommandService.logOffUser("<EMAIL>");

        // Then - 验证执行结果
        assertTrue(result);
        verify(userRepository).find(testEmail);
        verify(userRepository).delete(mockUser.getUserId());
        verify(streamProducer).streamSfUserMsg(SfUserMessage.TypeEnum.DELETE, mockUser);
    }

    @Test
    @DisplayName("注销用户 - 用户不存在异常")
    void testLogOffUser_UserNotFound() {
        // Given - 准备用户不存在的测试数据
        Email testEmail = new Email("<EMAIL>");
        when(userRepository.find(testEmail)).thenReturn(null);

        // When & Then - 执行方法并验证异常
        assertThrows(UserCenterException.class, () -> userCommandService.logOffUser("<EMAIL>"));
        verify(userRepository).find(testEmail);
    }

    @Test
    @DisplayName("上报手机信息 - 成功场景")
    void testReportPhoneInfo_Success() {
        // Given - 准备上报手机信息的测试数据
        Long userId = 1L;

        // When - 执行上报手机信息方法
        userCommandService.reportPhoneInfo(userId, phoneInfoDto);

        // Then - 验证执行结果
        verify(userRepository).updateUserPhone(any(User.class));
    }

    @Test
    @DisplayName("编辑用户信息 - 成功场景")
    void testEditUserInfo_Success() {
        // Given - 准备编辑用户信息的测试数据
        // 使用已初始化的 userEditDto

        // When - 执行编辑用户信息方法
        userCommandService.editUserInfo(userEditDto);

        // Then - 验证执行结果
        verify(userRepository).update(any(UserDo.class), any());
        verify(streamProducer).streamSfUserMsg(eq(SfUserMessage.TypeEnum.UPDATE), any(User.class));
    }

    @Test
    @DisplayName("编辑用户信息 - ID为空场景")
    void testEditUserInfo_NullId() {
        // Given - 准备ID为空的测试数据
        userEditDto.setId(null);

        // When - 执行编辑用户信息方法
        userCommandService.editUserInfo(userEditDto);

        // Then - 验证不执行任何操作
        verify(userRepository, never()).update(any(), any());
        verify(streamProducer, never()).streamSfUserMsg(any(), any());
    }

    @Test
    @DisplayName("编辑用户头像 - 成功场景")
    void testEditUserPhoto_Success() {
        // Given - 准备编辑用户头像的测试数据
        // 使用已初始化的 userEditDto

        // When - 执行编辑用户头像方法
        userCommandService.editUserPhoto(userEditDto);

        // Then - 验证执行结果
        verify(userRepository).update(any(UserDo.class), any());
    }

    @Test
    @DisplayName("编辑用户头像 - ID为空场景")
    void testEditUserPhoto_NullId() {
        // Given - 准备ID为空的测试数据
        userEditDto.setId(null);

        // When - 执行编辑用户头像方法
        userCommandService.editUserPhoto(userEditDto);

        // Then - 验证不执行任何操作
        verify(userRepository, never()).update(any(), any());
    }

    @Test
    @DisplayName("编辑用户在线状态 - 在线状态成功场景")
    void testEditUserPresenceState_OnlineSuccess() {
        // Given - 准备编辑用户在线状态的测试数据
        Long userId = 1L;
        String appPresenceCode = "online";

        // When - 执行编辑用户在线状态方法
        userCommandService.editUserPresenceState(userId, appPresenceCode);

        // Then - 验证执行结果
        verify(userRepository).updateById(any(UserDo.class));
    }

    @Test
    @DisplayName("编辑用户在线状态 - 离线状态成功场景")
    void testEditUserPresenceState_OfflineSuccess() {
        // Given - 准备编辑用户离线状态的测试数据
        Long userId = 1L;
        String appPresenceCode = "offline";

        // When - 执行编辑用户在线状态方法
        userCommandService.editUserPresenceState(userId, appPresenceCode);

        // Then - 验证执行结果
        verify(userRepository).updateById(any(UserDo.class));
    }

    @Test
    @DisplayName("编辑用户在线状态 - 无效状态场景")
    void testEditUserPresenceState_InvalidState() {
        // Given - 准备无效状态的测试数据
        Long userId = 1L;
        String appPresenceCode = "invalid";

        // When - 执行编辑用户在线状态方法
        userCommandService.editUserPresenceState(userId, appPresenceCode);

        // Then - 验证不执行任何操作
        verify(userRepository, never()).updateById(any());
    }

    @Test
    @DisplayName("编辑用户在线状态 - 空参数场景")
    void testEditUserPresenceState_NullParams() {
        // Given - 准备空参数的测试数据
        // 无需额外准备

        // When - 执行编辑用户在线状态方法（空参数）
        userCommandService.editUserPresenceState(null, "online");
        userCommandService.editUserPresenceState(1L, null);

        // Then - 验证不执行任何操作
        verify(userRepository, never()).updateById(any());
    }

    @Test
    @DisplayName("压力测试用户注册 - 成功场景")
    void testTestRegisterUser_Success() {
        // Given - 准备压力测试的测试数据
        Integer start = 1;
        Integer count = 5;

        // When - 执行压力测试用户注册方法
        userCommandService.testRegisterUser(start, count);

        // Then - 验证执行结果
        verify(userRepository).saveBatch(anyList());
    }

    @Test
    @DisplayName("从SF同步用户 - 空列表场景")
    void testSyncUserFromSf_EmptyList() {
        // Given - 准备空列表的测试数据
        List<SfUserRecord> emptyList = new ArrayList<>();

        // When - 执行从SF同步用户方法
        userCommandService.syncUserFromSf(emptyList);

        // Then - 验证不执行任何操作
        verify(userRepository, never()).list(any());
        verify(userRepository, never()).saveBatch(any());
        verify(userRepository, never()).updateBatchById(any());
    }

    @Test
    @DisplayName("从SF同步用户 - null列表场景")
    void testSyncUserFromSf_NullList() {
        // Given - 准备null列表的测试数据
        // 无需额外准备

        // When - 执行从SF同步用户方法
        userCommandService.syncUserFromSf(null);

        // Then - 验证不执行任何操作
        verify(userRepository, never()).list(any());
        verify(userRepository, never()).saveBatch(any());
        verify(userRepository, never()).updateBatchById(any());
    }

    @Test
    @DisplayName("从SF同步用户 - 新用户添加场景")
    void testSyncUserFromSf_AddNewUser() {
        // Given - 准备新用户添加的测试数据
        List<SfUserRecord> sfUserRecords = createMockSfUserRecords();
        List<UserDo> existingUsers = new ArrayList<>(); // 空列表，表示没有现有用户
        when(userRepository.list(any())).thenReturn(existingUsers);

        // When - 执行从SF同步用户方法
        userCommandService.syncUserFromSf(sfUserRecords);

        // Then - 验证执行结果
        verify(userRepository).list(any());
        verify(userRepository).saveBatch(anyList());
        verify(userRepository, never()).updateBatchById(any());
    }

    /**
     * 创建模拟的SF用户记录
     *
     * @return SF用户记录列表
     */
    private List<SfUserRecord> createMockSfUserRecords() {
        List<SfUserRecord> records = new ArrayList<>();
        SfUserRecord record = new SfUserRecord();
        record.setSfUserId("SF001");
        record.setUsername("<EMAIL>");
        record.setPassword("password123");
        record.setFirstName("John");
        record.setLastName("Doe");
        records.add(record);
        return records;
    }
}
