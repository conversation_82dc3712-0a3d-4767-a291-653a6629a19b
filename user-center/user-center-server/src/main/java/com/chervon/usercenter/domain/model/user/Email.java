package com.chervon.usercenter.domain.model.user;

import cn.hutool.core.util.ReUtil;
import com.chervon.common.core.domain.ValueObject;
import lombok.Getter;
import lombok.ToString;
import org.apache.commons.lang3.Validate;

import java.util.Objects;

/**
 * 邮箱
 *
 * <AUTHOR>
 * @date 2022-06-14
 **/
@Getter
@ToString
public final class Email implements ValueObject<Email> {

    private final String email;

    /**
     * Constructor.
     *
     * @param email
     */
    public Email(final String email) {
        this.email = email;
    }

    @Override
    public boolean sameValueAs(Email other) {
        return other != null && this.email.equals(other.email);
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null || getClass() != obj.getClass()) {
            return false;
        }
        Email email1 = (Email) obj;
        return Objects.equals(email, email1.email);
    }

    @Override
    public int hashCode() {
        return Objects.hash(email);
    }

}
