package com.chervon.feedback.service;

import com.chervon.common.core.domain.PageResult;
import com.chervon.common.core.utils.ConvertUtil;
import com.chervon.common.core.utils.JsonUtils;
import com.chervon.common.i18n.util.MessageTools;
import com.chervon.common.sso.CurrentLoginUtil;
import com.chervon.feedback.api.dto.AppFeedbackEditDto;
import com.chervon.feedback.api.dto.AppFeedbackReqDto;
import com.chervon.feedback.api.service.RemoteFeedbackService;
import com.chervon.feedback.api.vo.AppFeedbackPageVo;
import com.chervon.feedback.api.vo.AppFeedbackStatusVo;
import com.chervon.feedback.entity.Feedback;
import com.chervon.feedback.entity.enums.StaticMultiLanguageEnum;
import com.chervon.feedback.req.FeedbackPageDto;
import com.chervon.feedback.resp.FeedbackPageVo;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.Arrays;
import java.util.List;

/**
 * @Author：flynn.wang
 * @Date：2024/5/14 15:23
 */
@SpringBootTest
public class TestMainService {

    @Autowired
    private MainService mainService;

    @Autowired
    private RemoteFeedbackService remoteFeedbackService;

    @Autowired
    private MessageTools messageTools;
    @Test
    public void testPushFeedbackMsg() {
        Feedback feedback=new Feedback();
        feedback.setUserId(1759857344404881409L);
        feedback.setId(1789952656324001793L);
        feedback.setReplyContent("test reply content");
        mainService.pushFeedbackMsg(feedback);
    }

    @Test
    public void testGetFeedbackMsgTitle(){
        System.out.println(messageTools.getCodeValue(StaticMultiLanguageEnum.FEEDBACK_MESSAGE_TITLE.getCode(),"en"));
        System.out.println(messageTools.getCodeValue(StaticMultiLanguageEnum.FEEDBACK_MESSAGE_TITLE.getCode(),"zh"));
    }

    @Test
    public void testPageFeedback() {
        FeedbackPageDto dto = new FeedbackPageDto();
//        dto.setUserId("1810866973848305665");
        dto.setEmail("<EMAIL>");
        List<FeedbackPageVo> feedbacks = mainService.page(dto).getList();
        for (FeedbackPageVo feedback : feedbacks) {
            System.out.println(feedback.getFeedbackId() + " " + feedback.getEmail() + " " + feedback.getFeedbackContent());
        }
    }

    @Test
    public void testCloseFeedback() {
        mainService.closeFeedback(1644214912669401089L);
    }

    @Test
    public void testEditFeedback() {
        AppFeedbackEditDto editDto = new AppFeedbackEditDto();
        editDto.setId(1644264937387233281L);
        editDto.setType(2);
        editDto.setPictures(Arrays.asList("http://www.baidu.com/1.jpg", "http://www.baidu.com/2.jpg"));
        remoteFeedbackService.edit(editDto);
    }

    @Test
    public void testHasFeedback() {
        AppFeedbackReqDto reqDto = new AppFeedbackReqDto();
        reqDto.setType(2);
        reqDto.setUserId(1621052854666670082L);
        reqDto.setDeviceId("XTR012215444568");
        AppFeedbackStatusVo vo = remoteFeedbackService.hasFeedback(reqDto);
        System.out.println(JsonUtils.toJson(vo));
    }

}
