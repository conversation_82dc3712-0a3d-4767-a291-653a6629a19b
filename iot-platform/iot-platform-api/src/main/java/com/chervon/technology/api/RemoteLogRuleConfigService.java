package com.chervon.technology.api;

import com.chervon.technology.api.dto.LogRuleQueryDto;
import com.chervon.technology.api.vo.log.rule.LogRuleDetailVo;
import java.util.List;
import java.util.Map;

/**
 * 日志规则配置service
 * <AUTHOR>
 * @since 2024-11-04 14:26
 **/
public interface RemoteLogRuleConfigService {
    /**
     * 获取日志规则详情列表
     * @param queryDto
     * @return
     */
    List<LogRuleDetailVo> getRuleList(LogRuleQueryDto queryDto);

    /**
     * 统计日志模板引用次数
     * @param listTemplateId
     * @return
     */
    Map<Long, Long> selectLogTemplateCount(List<Long> listTemplateId);

}
