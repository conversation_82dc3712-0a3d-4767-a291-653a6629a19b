package com.chervon.technology.util;

import com.chervon.common.redis.utils.RedisUtils;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.time.Duration;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * 通用本地缓存工具类
 * <AUTHOR> 2024/10/14
 */
@Slf4j
public class CacheUtil {

    // 存缓存数据
    private final static Map<String, CacheEntity> CACHE_MAP = new ConcurrentHashMap<>();

    // 定时器线程池，用于清理过期缓存
    private static ScheduledExecutorService executorService = Executors.newSingleThreadScheduledExecutor();

    static {
        // 注册一个定时任务，服务启动 60秒后，每隔 120秒执行一次清理过期缓存的任务
        Runnable task = CacheUtil::clear;
        executorService.scheduleAtFixedRate(task, 10L, 20L, TimeUnit.SECONDS);
        log.info("fleet-service CacheUtil缓存回收任务已启动");
    }

    // 添加缓存
    public static void putLocal(String key, Object value, Long expireSecond) {
        CacheEntity cacheEntity = new CacheEntity();
        cacheEntity.setKey(key);
        cacheEntity.setValue(value);
        if (expireSecond > 0) {
            // 设置过期时间
            cacheEntity.setExpireTime(System.currentTimeMillis() + Duration.ofSeconds(expireSecond).toMillis());
        }
        CACHE_MAP.put(key, cacheEntity);
    }

    // 获取
    public static Object getLocal(String key) {
        if (CACHE_MAP.containsKey(key)) {
            //判断是否过期
            if (CACHE_MAP.get(key).getExpireTime() != null && CACHE_MAP.get(key).getExpireTime() < System.currentTimeMillis()) {
                return null;
            }
            return CACHE_MAP.get(key).getValue();
        }
        return null;
    }

    // 业务逻辑控制移除缓存方法
    public static void removeLocal(String key) {
        CACHE_MAP.remove(key);
    }

    // 清除过期缓存
    public static void clear() {
        if (CACHE_MAP.isEmpty()) {
            return;
        }
        log.info("CacheUtil缓存回收前,缓存数：{}",CACHE_MAP.size());
        CACHE_MAP.entrySet().removeIf(entityEntry -> entityEntry.getValue().getExpireTime() != null && entityEntry.getValue().getExpireTime() < System.currentTimeMillis());
        log.info("CacheUtil缓存回收后,缓存数：{}",CACHE_MAP.size());
    }
    //*********************************redis缓存工具*******************************************

    public static void putRedis(String key, Object value){
        RedisUtils.setCacheObject(key,value);
    }
    public static void putRedisExpire(String key,Object value,Long expire){
        RedisUtils.setWithExpire(key,value,expire);
    }
    public static void removeRedis(String key) {
        RedisUtils.deleteObject(key);
    }
    public static <T> T getRedis(String key) {
        return RedisUtils.getCacheObject(key);
    }

}
@Data
class CacheEntity {
    // 缓存键
    private String key;
    // 缓存键
    private Object value;
    // 过期时间
    private Long expireTime;
}

