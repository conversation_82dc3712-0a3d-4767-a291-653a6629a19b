package com.chervon.technology.rpc;

import com.chervon.technology.api.RemoteLogRuleConfigService;
import com.chervon.technology.api.dto.LogRuleQueryDto;
import com.chervon.technology.api.vo.log.rule.LogRuleDetailVo;
import com.chervon.technology.service.LogRuleConfigService;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 日志规则配置service实现类
 * <AUTHOR> 2024/11/7
 */
@DubboService
@Service
public class RemoteLogRuleConfigServiceImpl  implements RemoteLogRuleConfigService {

    @Resource
    private LogRuleConfigService logRuleConfigService;

    /**
     * 获取全量日志规则配置及规则详情内容
     * @param queryDto 参数
     * @return
     */
    @Override
    public List<LogRuleDetailVo> getRuleList(LogRuleQueryDto queryDto) {
        return logRuleConfigService.getRuleList(queryDto);
    }

    /**
     * 根据消息模板分组统计引用数量
     * @param listTemplateId 消息模板id
     * @return
     */
    @Override
    public Map<Long, Long> selectLogTemplateCount(List<Long> listTemplateId){
        final List<Map<Long, Long>> mapList = logRuleConfigService.selectLogTemplateCount(listTemplateId);
        Map<Long, Long> map=new HashMap<>();
        if(CollectionUtils.isEmpty(mapList)){
            return map;
        }
        for(Map<Long, Long> itemMap:mapList){
            Object id = itemMap.get("id");
            Object value=itemMap.get("rowCount");
            if(!Objects.isNull(id) && !Objects.isNull(value)){
                map.put((Long)id,(Long)value);
            }
        }
        return map;
    }
}
