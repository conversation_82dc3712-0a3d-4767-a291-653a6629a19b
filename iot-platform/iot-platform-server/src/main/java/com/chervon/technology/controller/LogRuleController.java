package com.chervon.technology.controller;

import com.chervon.common.core.domain.PageResult;
import com.chervon.iot.middle.api.dto.log.LogQueryDto;
import com.chervon.iot.middle.api.service.RemoteDeviceLogService;
import com.chervon.iot.middle.api.vo.log.LogResultVo;
import com.chervon.operation.api.RemoteMessageTemplateService;
import com.chervon.operation.api.vo.MessageTemplateVo;
import com.chervon.technology.api.dto.LogRuleQueryDto;
import com.chervon.technology.api.vo.log.rule.LogRuleDetailVo;
import com.chervon.technology.domain.dto.rule.log.LogRuleDto;
import com.chervon.technology.domain.vo.rule.log.LogRuleVo;
import com.chervon.technology.service.LogRuleConfigService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * app日志规则配置/日志模板接口
 * <AUTHOR>
 * @since 2024-11-06 10:11
 **/
@Api(tags = "app日志规则配置相关接口")
@RestController
@RequestMapping("/log")
public class LogRuleController {
    @Autowired
    private LogRuleConfigService logRuleConfigService;
    @DubboReference
    private RemoteDeviceLogService remoteDeviceLogService;
//    @DubboReference
//    private RemoteLogTemplateService remoteLogTemplateService;
    @DubboReference
    private RemoteMessageTemplateService remoteMessageTemplateService;
    /**
     * 添加日志规则
     *
     * @param logRuleAddDto 添加日志规则Dto
     * @return 添加结果
     */
    @ApiOperation("添加日志规则")
    @PostMapping("/rule/add")
    public void add(@RequestBody LogRuleDto logRuleAddDto) {
        logRuleConfigService.add(logRuleAddDto);
    }

    /**
     * 编辑日志规则
     * @param logRuleEditDto 编辑日志规则
     */
    @ApiOperation(value = "编辑日志规则")
    @PostMapping("/rule/edit")
    public void edit(@Validated @RequestBody LogRuleDto logRuleEditDto) {
        logRuleConfigService.edit(logRuleEditDto);
    }

    /**
     * 删除日志规则
     *
     * @param logRuleId 删除日志规则id
     * @return 返回body
     */
    @ApiOperation("根据消息故障id删除日志规则")
    @GetMapping("/rule/delete")
    public void delete(@RequestParam Long logRuleId) {
        logRuleConfigService.delete(logRuleId);
    }

    /**
     * 启用禁用日志规则状态
     *
     * @param logRuleDto 编辑状态Dto
     * @return 编辑结果
     */
    @ApiOperation(value = "状态启用停用")
    @PostMapping("/rule/enabled")
    public void enabled(@RequestBody LogRuleDto logRuleDto) {
        logRuleConfigService.editStatus(logRuleDto);
    }

    /**
     * 日志规则分页列表
     * @param queryDto 分页参数
     * @return 返回列表
     */
    @ApiOperation("日志规则配置分页列表")
    @PostMapping("/rule/page")
    public PageResult<LogRuleVo> rulePageList(@RequestBody LogRuleQueryDto queryDto) {
        return logRuleConfigService.rulePageList(queryDto);
    }
    /**
     * 日志规则详情
     * @param logRuleId 日志规则id
     * @return 返回详情Vo
     */
    @ApiOperation("日志规则配置详情")
    @GetMapping("/rule/detail")
    public LogRuleDetailVo detail(@RequestParam Long logRuleId) {
        return logRuleConfigService.detail(logRuleId);
    }

    /**
     * app日志分页列表
     * @param logQueryDto 查询条件
     * @return 分页结果
     */
    @ApiOperation("app日志分页列表")
    @PostMapping("/result/page")
    public PageResult<LogResultVo> logResultPageList(@RequestBody LogQueryDto logQueryDto) {
        PageResult<LogResultVo> appDeviceLog = remoteDeviceLogService.getAppDeviceLog(logQueryDto);
        if (CollectionUtils.isEmpty(appDeviceLog.getList())) {
            return appDeviceLog;
        }
        List<Long> listTemplateId = appDeviceLog.getList().stream().map(a -> a.getLogTemplateId()).distinct().collect(Collectors.toList());
        final List<MessageTemplateVo> messageTemplateVos = remoteMessageTemplateService.listByTemplateIds(listTemplateId);
        if (CollectionUtils.isEmpty(messageTemplateVos)) {
            return appDeviceLog;
        }
        for (LogResultVo logResultVo : appDeviceLog.getList()) {
            Optional<MessageTemplateVo> first = messageTemplateVos.stream().filter(a -> a.getId().equals(logResultVo.getLogTemplateId())).findFirst();
            if (first.isPresent()) {
                logResultVo.setTitle(first.get().getTitle());
            }
        }
        if(StringUtils.hasText(logQueryDto.getTitle()) && !CollectionUtils.isEmpty(appDeviceLog.getList())){
            List<LogResultVo> collect = appDeviceLog.getList().stream().filter(a -> a.getTitle().toLowerCase().contains(logQueryDto.getTitle().toLowerCase())).collect(Collectors.toList());
            appDeviceLog.setList(collect);
            if(CollectionUtils.isEmpty(collect)){
                appDeviceLog.setTotal(0);
                appDeviceLog.setPageNum(0);
                appDeviceLog.setPages(0);
            }
        }
        return appDeviceLog;
    }

//    /**
//     * 日志模板下拉列表
//     * @return 分页结果
//     */
//    @ApiOperation("日志模板下拉列表")
//    @PostMapping("/template/list")
//    public List<RemoteSpinnerVo> templateList() {
//        return remoteLogTemplateService.listSpinner(LocaleContextHolder.getLocale().getLanguage(),null);
//    }
}
