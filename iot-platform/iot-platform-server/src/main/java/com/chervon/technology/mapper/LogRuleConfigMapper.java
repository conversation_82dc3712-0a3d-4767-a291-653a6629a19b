package com.chervon.technology.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.chervon.technology.domain.dataobject.LogRuleConfig;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Map;

/**
 * 日志规则配置主表(log_rule_config)数据Mapper
 *
 * <AUTHOR>
 * @since 2024-11-04 10:36:00
 * @description 日志规则配置主表
*/
@Mapper
public interface LogRuleConfigMapper extends BaseMapper<LogRuleConfig> {

    @Select("<script>" +
            "SELECT log_template_id as id, COUNT(1) AS rowCount FROM log_rule_config WHERE is_deleted = 0 " +
            "AND log_template_id IN " +
            "<foreach item='id' collection='listTemplateId' open='(' separator=',' close=')'>" +
            "#{id}" +
            "</foreach> " +
            "GROUP BY log_template_id" +
            "</script>")
    List<Map<Long, Long>> selectLogTemplateCount(@Param("listTemplateId") List<Long> listTemplateId);
}
