package com.chervon.technology.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.chervon.common.core.domain.PageResult;
import com.chervon.technology.api.vo.log.rule.LogRuleDetailVo;
import com.chervon.technology.domain.dataobject.LogRuleConfig;
import com.chervon.technology.domain.dto.rule.log.LogRuleDto;
import com.chervon.technology.api.dto.LogRuleQueryDto;
import com.chervon.technology.domain.vo.rule.log.LogRuleVo;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Map;

/**
 * 日志规则配置service
 * <AUTHOR>
 * @since 2024-11-04 14:26
 **/
public interface LogRuleConfigService extends IService<LogRuleConfig> {
    /**
     * 添加日志规则配置
     *
     * @param logRuleAddDto 添加日志规则配置
     */
    void add(LogRuleDto logRuleAddDto);

    /**
     * 修改日志规则配置
     *
     * @param logRuleEditDto 修改日志规则配置
     */
    void edit(LogRuleDto logRuleEditDto);

    /**
     * 删除日志规则
     * @param logRuleId 删除日志规则id
     */
    void delete(Long logRuleId);

    /**
     * 启用禁用状态编辑：0-禁用，1-启用
     * id,enabled字段必填
     * @param enableStatus 启用禁用编辑
     */
    void editStatus(LogRuleDto enableStatus);

    /**
     * 日志规则分页列表
     * @param queryDto 参数
     * @return 分页返回值
     */
    PageResult<LogRuleVo> rulePageList(LogRuleQueryDto queryDto);
    /**
     * 详情
     *
     * @param logRuleId 参数
     * @return 详情
     */
    LogRuleDetailVo detail(Long logRuleId);

    /**
     * 获取日志规则详情列表
     * @param queryDto
     * @return
     */
    List<LogRuleDetailVo> getRuleList(LogRuleQueryDto queryDto);


    List<Map<Long, Long>> selectLogTemplateCount(List<Long> listTemplateId);

}
