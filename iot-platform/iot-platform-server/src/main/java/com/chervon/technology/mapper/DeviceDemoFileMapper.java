package com.chervon.technology.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.chervon.iot.middle.api.vo.device.DeviceTopologyVo;
import com.chervon.technology.api.dto.DeviceProductDto;
import com.chervon.technology.api.vo.DeviceRpcVo;
import com.chervon.technology.domain.dataobject.Device;
import com.chervon.technology.domain.dataobject.DeviceDemoFile;
import com.chervon.technology.domain.dto.device.SearchDeviceDto;
import com.chervon.technology.domain.vo.device.DeviceDetailVo;
import com.chervon.technology.domain.vo.device.DeviceListVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 设备示例
 *
 */
@Mapper
public interface DeviceDemoFileMapper extends BaseMapper<DeviceDemoFile> {


}
