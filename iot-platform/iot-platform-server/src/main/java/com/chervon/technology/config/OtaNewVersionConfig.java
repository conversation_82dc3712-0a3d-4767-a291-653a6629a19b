package com.chervon.technology.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/2/20 21:18
 */
@Data
@Component
@RefreshScope
@ConfigurationProperties(prefix = "ota.new-version")
public class OtaNewVersionConfig {

    /**
     * 设备SN及新版本配置（支持差分升级包）
     */
    private Map<String,String> snVersionList = new HashMap<>();
}
