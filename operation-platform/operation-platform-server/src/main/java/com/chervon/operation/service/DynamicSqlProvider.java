package com.chervon.operation.service;

import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.jdbc.SQL;
import org.springframework.util.CollectionUtils;

import java.util.Map;

import static org.apache.ibatis.jdbc.SqlBuilder.FROM;
import static org.apache.ibatis.jdbc.SqlBuilder.SELECT;

/**
 * 动态sql提供者
 * <AUTHOR> 2024/12/10
 */
public class DynamicSqlProvider {
    public String selectFromDynamicTable(@Param("templateName") String tableName, @Param("conditions") Map<String, Object> conditions) {
        //通过模板名查询配置的sql
        String sql="select id as template_id, name->>'$.en' as name_de,name->>'$.de' as name_de,name->>'$.fr' as name_fr,name->>'$.nl' as name_nl\n" +
                ", title->>'$.en' as title_en,title->>'$.de' as title_de,title->>'$.fr' as title_fr,title->>'$.nl' as title_nl\n" +
                "from log_template where is_deleted=0 ";
//        if(!CollectionUtils.isEmpty(conditions)){
//            sql += "and name->>'$.en' like '%" + conditions.get("name_en") + "%'";
//        }
        return sql;
    }
}
